// Package models contains core data structures for the decision tree implementation.
package models

import (
	"fmt"
	"math"
)

// NodeType defines the type of node in the decision tree
type NodeType string

const (
	DecisionNode NodeType = "decision" // Internal node that splits data
	LeafNode     NodeType = "leaf"     // Terminal node with prediction
)

// TreeNode represents a node in a decision tree
type TreeNode struct {
	Type              NodeType                  `json:"type"`               // Node type (decision or leaf)
	Feature           *Feature                  `json:"feature,omitempty"`  // Feature used for splitting
	Threshold         float64                   `json:"threshold"`          // Threshold for numerical splits (legacy)
	ThresholdInt64    int64                     `json:"threshold_int64"`    // Threshold for datetime features (int64)
	Categories        map[interface{}]*TreeNode `json:"categories"`         // Children for categorical features
	Left              *TreeNode                 `json:"left"`               // Left child
	Right             *TreeNode                 `json:"right"`              // Right child
	Prediction        interface{}               `json:"prediction"`         // Prediction value for leaf nodes
	ClassDistribution map[interface{}]int       `json:"class_distribution"` // Class distribution at this node
	Samples           int                       `json:"samples"`            // Sample count at this node
	Confidence        float64                   `json:"confidence"`         // Confidence in prediction (0-1)
	Impurity          float64                   `json:"impurity"`           // Impurity measure at this node

	// DateTime-specific metadata for better interpretability
	DateTimeInfo *DateTimeSplitInfo `json:"datetime_info,omitempty"` // Human-readable datetime split info
}

// DateTimeSplitInfo provides human-readable datetime split information
type DateTimeSplitInfo struct {
	ThresholdFormatted string `json:"threshold_formatted"` // "2024-12-01 14:30:45"
	SplitDescription   string `json:"split_description"`   // "Before December 1, 2024 14:30"
}

// NewDecisionNode creates a decision node for numerical features with validation
func NewDecisionNode(feature *Feature, threshold float64) (*TreeNode, error) {
	if feature == nil {
		return nil, &ModelError{
			Op:     "create_decision_node",
			Field:  "feature",
			Reason: "feature cannot be nil",
		}
	}

	if feature.Type != NumericFeature && feature.Type != DateFeature {
		return nil, &ModelError{
			Op:     "create_decision_node",
			Field:  "feature_type",
			Value:  string(feature.Type),
			Reason: "decision node with threshold requires numeric or date feature",
		}
	}

	if math.IsInf(threshold, 0) || math.IsNaN(threshold) {
		return nil, &ModelError{
			Op:     "create_decision_node",
			Field:  "threshold",
			Value:  fmt.Sprintf("%.6f", threshold),
			Reason: "threshold cannot be infinite or NaN",
		}
	}

	return &TreeNode{
		Type:       DecisionNode,
		Feature:    feature,
		Threshold:  threshold,
		Categories: make(map[interface{}]*TreeNode),
		Samples:    0,
		Confidence: 0.0,
		Impurity:   0.0,
	}, nil
}

// NewDateTimeDecisionNode creates a decision node for datetime features with int64 threshold
func NewDateTimeDecisionNode(feature *Feature, thresholdInt64 int64, dateTimeInfo *DateTimeSplitInfo) (*TreeNode, error) {
	if feature == nil {
		return nil, &ModelError{
			Op:     "create_datetime_decision_node",
			Field:  "feature",
			Reason: "feature cannot be nil",
		}
	}

	if feature.Type != DateFeature {
		return nil, &ModelError{
			Op:     "create_datetime_decision_node",
			Field:  "feature_type",
			Value:  string(feature.Type),
			Reason: "datetime decision node requires date feature",
		}
	}

	// Validate int64 datetime range
	if thresholdInt64 < 10000101000000 || thresholdInt64 > 99991231235959 {
		return nil, &ModelError{
			Op:     "create_datetime_decision_node",
			Field:  "threshold_int64",
			Value:  fmt.Sprintf("%d", thresholdInt64),
			Reason: "datetime threshold outside valid range (10000101000000-99991231235959)",
		}
	}

	return &TreeNode{
		Type:           DecisionNode,
		Feature:        feature,
		ThresholdInt64: thresholdInt64,
		Categories:     make(map[interface{}]*TreeNode),
		Samples:        0,
		Confidence:     0.0,
		Impurity:       0.0,
		DateTimeInfo:   dateTimeInfo,
	}, nil
}

// NewCategoricalDecisionNode creates a decision node for categorical features with validation
func NewCategoricalDecisionNode(feature *Feature) (*TreeNode, error) {
	if feature == nil {
		return nil, &ModelError{
			Op:     "create_categorical_decision_node",
			Field:  "feature",
			Reason: "feature cannot be nil",
		}
	}

	if feature.Type != CategoricalFeature {
		return nil, &ModelError{
			Op:     "create_categorical_decision_node",
			Field:  "feature_type",
			Value:  string(feature.Type),
			Reason: "categorical decision node requires categorical feature",
		}
	}

	return &TreeNode{
		Type:       DecisionNode,
		Feature:    feature,
		Categories: make(map[interface{}]*TreeNode),
		Samples:    0,
		Confidence: 0.0,
		Impurity:   0.0,
	}, nil
}

// NewLeafNode creates a terminal node with prediction and validation
func NewLeafNode(prediction interface{}, distribution map[interface{}]int, samples int) (*TreeNode, error) {
	if prediction == nil {
		return nil, &ModelError{
			Op:     "create_leaf_node",
			Field:  "prediction",
			Reason: "prediction cannot be nil",
		}
	}

	if samples < 0 {
		return nil, &ModelError{
			Op:     "create_leaf_node",
			Field:  "samples",
			Value:  fmt.Sprintf("%d", samples),
			Reason: "sample count cannot be negative",
		}
	}

	if distribution == nil {
		distribution = make(map[interface{}]int)
	}

	// Validate that distribution sums to samples
	totalSamples := 0
	for _, count := range distribution {
		if count < 0 {
			return nil, &ModelError{
				Op:     "create_leaf_node",
				Field:  "class_distribution",
				Reason: "class counts cannot be negative",
			}
		}
		totalSamples += count
	}

	if samples > 0 && totalSamples != samples {
		return nil, &ModelError{
			Op:     "create_leaf_node",
			Field:  "class_distribution",
			Reason: fmt.Sprintf("distribution total (%d) does not match sample count (%d)", totalSamples, samples),
		}
	}

	// Calculate confidence and impurity
	confidence := 0.0
	impurity := 0.0

	if samples > 0 {
		// Calculate purity (confidence)
		maxCount := 0
		for _, count := range distribution {
			if count > maxCount {
				maxCount = count
			}
		}
		confidence = float64(maxCount) / float64(samples)

		// Calculate Gini impurity
		impurity = 1.0
		for _, count := range distribution {
			if count > 0 {
				prob := float64(count) / float64(samples)
				impurity -= prob * prob
			}
		}
	}

	return &TreeNode{
		Type:              LeafNode,
		Prediction:        prediction,
		ClassDistribution: distribution,
		Samples:           samples,
		Confidence:        confidence,
		Impurity:          impurity,
	}, nil
}

// IsLeaf checks if node is a leaf node
func (n *TreeNode) IsLeaf() bool {
	return n.Type == LeafNode
}

// Validate performs comprehensive validation of the tree node
func (n *TreeNode) Validate() error {
	if n.Samples < 0 {
		return &ModelError{
			Op:     "validate_node",
			Field:  "samples",
			Value:  fmt.Sprintf("%d", n.Samples),
			Reason: "sample count cannot be negative",
		}
	}

	if n.Confidence < 0.0 || n.Confidence > 1.0 {
		return &ModelError{
			Op:     "validate_node",
			Field:  "confidence",
			Value:  fmt.Sprintf("%.6f", n.Confidence),
			Reason: "confidence must be between 0.0 and 1.0",
		}
	}

	if n.Impurity < 0.0 || n.Impurity > 1.0 {
		return &ModelError{
			Op:     "validate_node",
			Field:  "impurity",
			Value:  fmt.Sprintf("%.6f", n.Impurity),
			Reason: "impurity must be between 0.0 and 1.0",
		}
	}

	if n.IsLeaf() {
		return n.validateLeafNode()
	}

	return n.validateDecisionNode()
}

// validateLeafNode validates leaf node specific constraints
func (n *TreeNode) validateLeafNode() error {
	if n.Prediction == nil {
		return &ModelError{
			Op:     "validate_leaf_node",
			Field:  "prediction",
			Reason: "leaf node must have a prediction",
		}
	}

	// Validate class distribution consistency
	totalSamples := 0
	for class, count := range n.ClassDistribution {
		if count < 0 {
			return &ModelError{
				Op:     "validate_leaf_node",
				Field:  "class_distribution",
				Value:  fmt.Sprintf("%v", class),
				Reason: "class counts cannot be negative",
			}
		}
		totalSamples += count
	}

	if n.Samples > 0 && totalSamples != n.Samples {
		return &ModelError{
			Op:     "validate_leaf_node",
			Field:  "class_distribution",
			Reason: fmt.Sprintf("distribution total (%d) does not match sample count (%d)", totalSamples, n.Samples),
		}
	}

	return nil
}

// validateDecisionNode validates decision node specific constraints
func (n *TreeNode) validateDecisionNode() error {
	if n.Feature == nil {
		return &ModelError{
			Op:     "validate_decision_node",
			Field:  "feature",
			Reason: "decision node must have a feature",
		}
	}

	// Validate feature-specific constraints
	switch n.Feature.Type {
	case NumericFeature, DateFeature:
		if math.IsInf(n.Threshold, 0) || math.IsNaN(n.Threshold) {
			return &ModelError{
				Op:     "validate_decision_node",
				Field:  "threshold",
				Value:  fmt.Sprintf("%.6f", n.Threshold),
				Reason: "threshold cannot be infinite or NaN",
			}
		}

		// For numeric features, should have left/right children
		if n.Left == nil && n.Right == nil {
			return &ModelError{
				Op:     "validate_decision_node",
				Field:  "children",
				Reason: "numeric decision node should have left and/or right children",
			}
		}

	case CategoricalFeature:
		// For categorical features, should have category children
		if len(n.Categories) == 0 {
			return &ModelError{
				Op:     "validate_decision_node",
				Field:  "categories",
				Reason: "categorical decision node should have category children",
			}
		}

	default:
		return &ModelError{
			Op:     "validate_decision_node",
			Field:  "feature_type",
			Value:  string(n.Feature.Type),
			Reason: "unsupported feature type for decision node",
		}
	}

	return nil
}

// GetMajorityClass returns the class with the highest count in this node
func (n *TreeNode) GetMajorityClass() interface{} {
	if len(n.ClassDistribution) == 0 {
		return nil
	}

	var majorityClass interface{}
	maxCount := -1

	for class, count := range n.ClassDistribution {
		if count > maxCount {
			maxCount = count
			majorityClass = class
		}
	}

	return majorityClass
}

// GetPurity returns the purity of this node (ratio of majority class)
func (n *TreeNode) GetPurity() float64 {
	if n.Samples == 0 {
		return 0.0
	}

	majorityClass := n.GetMajorityClass()
	if majorityClass == nil {
		return 0.0
	}

	return float64(n.ClassDistribution[majorityClass]) / float64(n.Samples)
}

// GetImpurity returns the Gini impurity of this node
func (n *TreeNode) GetImpurity() float64 {
	if n.Samples == 0 {
		return 0.0
	}

	impurity := 1.0
	for _, count := range n.ClassDistribution {
		if count > 0 {
			prob := float64(count) / float64(n.Samples)
			impurity -= prob * prob
		}
	}

	return impurity
}

// UpdateStatistics recalculates confidence and impurity based on current distribution
func (n *TreeNode) UpdateStatistics() {
	if n.Samples == 0 {
		n.Confidence = 0.0
		n.Impurity = 0.0
		return
	}

	// Calculate confidence (purity)
	maxCount := 0
	for _, count := range n.ClassDistribution {
		if count > maxCount {
			maxCount = count
		}
	}
	n.Confidence = float64(maxCount) / float64(n.Samples)

	// Calculate Gini impurity
	n.Impurity = n.GetImpurity()
}

// AddSample adds a sample to this node's statistics
func (n *TreeNode) AddSample(class interface{}) error {
	if class == nil {
		return &ModelError{
			Op:     "add_sample",
			Field:  "class",
			Reason: "class cannot be nil",
		}
	}

	if n.ClassDistribution == nil {
		n.ClassDistribution = make(map[interface{}]int)
	}

	n.ClassDistribution[class]++
	n.Samples++
	n.UpdateStatistics()

	return nil
}

// GetChildCount returns the number of child nodes
func (n *TreeNode) GetChildCount() int {
	if n.IsLeaf() {
		return 0
	}

	count := 0
	if n.Left != nil {
		count++
	}
	if n.Right != nil {
		count++
	}
	count += len(n.Categories)

	return count
}

// GetChildren returns all child nodes
func (n *TreeNode) GetChildren() []*TreeNode {
	if n.IsLeaf() {
		return nil
	}

	var children []*TreeNode

	if n.Left != nil {
		children = append(children, n.Left)
	}
	if n.Right != nil {
		children = append(children, n.Right)
	}

	for _, child := range n.Categories {
		if child != nil {
			children = append(children, child)
		}
	}

	return children
}

// SetChild sets a child node for categorical features
func (n *TreeNode) SetChild(category interface{}, child *TreeNode) error {
	if n.IsLeaf() {
		return &ModelError{
			Op:     "set_child",
			Reason: "cannot set children on leaf node",
		}
	}

	if n.Feature == nil {
		return &ModelError{
			Op:     "set_child",
			Field:  "feature",
			Reason: "decision node must have a feature to set children",
		}
	}

	if n.Feature.Type != CategoricalFeature {
		return &ModelError{
			Op:     "set_child",
			Field:  "feature_type",
			Value:  string(n.Feature.Type),
			Reason: "can only set categorical children on categorical features",
		}
	}

	if category == nil {
		return &ModelError{
			Op:     "set_child",
			Field:  "category",
			Reason: "category cannot be nil",
		}
	}

	if n.Categories == nil {
		n.Categories = make(map[interface{}]*TreeNode)
	}

	n.Categories[category] = child
	return nil
}

// SetLeftChild sets the left child for numeric features
func (n *TreeNode) SetLeftChild(child *TreeNode) error {
	if n.IsLeaf() {
		return &ModelError{
			Op:     "set_left_child",
			Reason: "cannot set children on leaf node",
		}
	}

	if n.Feature == nil {
		return &ModelError{
			Op:     "set_left_child",
			Field:  "feature",
			Reason: "decision node must have a feature to set children",
		}
	}

	if n.Feature.Type != NumericFeature && n.Feature.Type != DateFeature {
		return &ModelError{
			Op:     "set_left_child",
			Field:  "feature_type",
			Value:  string(n.Feature.Type),
			Reason: "can only set left child on numeric or date features",
		}
	}

	n.Left = child
	return nil
}

// SetRightChild sets the right child for numeric features
func (n *TreeNode) SetRightChild(child *TreeNode) error {
	if n.IsLeaf() {
		return &ModelError{
			Op:     "set_right_child",
			Reason: "cannot set children on leaf node",
		}
	}

	if n.Feature == nil {
		return &ModelError{
			Op:     "set_right_child",
			Field:  "feature",
			Reason: "decision node must have a feature to set children",
		}
	}

	if n.Feature.Type != NumericFeature && n.Feature.Type != DateFeature {
		return &ModelError{
			Op:     "set_right_child",
			Field:  "feature_type",
			Value:  string(n.Feature.Type),
			Reason: "can only set right child on numeric or date features",
		}
	}

	n.Right = child
	return nil
}

// GetEffectiveThreshold returns the appropriate threshold value based on feature type
func (n *TreeNode) GetEffectiveThreshold() interface{} {
	if n.Feature != nil && n.Feature.Type == DateFeature {
		return n.ThresholdInt64
	}
	return n.Threshold
}

// SetDateTimeThreshold sets the int64 threshold and datetime info for date features
func (n *TreeNode) SetDateTimeThreshold(thresholdInt64 int64, dateTimeInfo *DateTimeSplitInfo) error {
	if n.IsLeaf() {
		return &ModelError{
			Op:     "set_datetime_threshold",
			Reason: "cannot set threshold on leaf node",
		}
	}

	if n.Feature == nil {
		return &ModelError{
			Op:     "set_datetime_threshold",
			Field:  "feature",
			Reason: "decision node must have a feature to set datetime threshold",
		}
	}

	if n.Feature.Type != DateFeature {
		return &ModelError{
			Op:     "set_datetime_threshold",
			Field:  "feature_type",
			Value:  string(n.Feature.Type),
			Reason: "can only set datetime threshold on date features",
		}
	}

	// Validate int64 datetime range
	if thresholdInt64 < 10000101000000 || thresholdInt64 > 99991231235959 {
		return &ModelError{
			Op:     "set_datetime_threshold",
			Field:  "threshold_int64",
			Value:  fmt.Sprintf("%d", thresholdInt64),
			Reason: "datetime threshold outside valid range (10000101000000-99991231235959)",
		}
	}

	n.ThresholdInt64 = thresholdInt64
	n.DateTimeInfo = dateTimeInfo
	return nil
}

// IsDateTimeNode checks if this node uses datetime features
func (n *TreeNode) IsDateTimeNode() bool {
	return n.Feature != nil && n.Feature.Type == DateFeature
}

// GetDateTimeThreshold returns the int64 threshold for datetime features
func (n *TreeNode) GetDateTimeThreshold() (int64, error) {
	if !n.IsDateTimeNode() {
		return 0, &ModelError{
			Op:     "get_datetime_threshold",
			Field:  "feature_type",
			Value:  string(n.Feature.Type),
			Reason: "node does not use datetime features",
		}
	}
	return n.ThresholdInt64, nil
}

// GetFormattedDateTimeThreshold returns a human-readable datetime threshold
func (n *TreeNode) GetFormattedDateTimeThreshold() string {
	if n.DateTimeInfo != nil {
		return n.DateTimeInfo.ThresholdFormatted
	}
	if n.IsDateTimeNode() {
		return fmt.Sprintf("%014d", n.ThresholdInt64)
	}
	return ""
}

// GetDateTimeSplitDescription returns a human-readable split description
func (n *TreeNode) GetDateTimeSplitDescription() string {
	if n.DateTimeInfo != nil {
		return n.DateTimeInfo.SplitDescription
	}
	if n.IsDateTimeNode() {
		return fmt.Sprintf("Before datetime %014d", n.ThresholdInt64)
	}
	return ""
}
