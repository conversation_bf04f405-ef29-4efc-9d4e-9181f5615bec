// Package utils provides utilities for converting ISO 8601 formatted date/time strings
// into numeric formats for data processing.
//
// This utility focuses on converting ISO 8601 formatted strings in the format:
// YYYY-MM-DDTHH:mm:ss.sssZ (where T separates date and time, Z indicates UTC or offset)
//
// Conversion rules:
// - Date only: YYYYMMDD (8-digit number)
// - Time only: HHMMSS (6-digit number, milliseconds ignored)
// - Date and time: YYYYMMDDHHMMSS (14-digit number)
//
// Example usage:
//
//	converter := utils.NewDateTimeConverter()
//
//	// Convert date only
//	result, err := converter.ConvertISO8601("2023-12-25")
//	// result: 20231225
//
//	// Convert time only
//	result, err := converter.ConvertISO8601("14:30:45.123Z")
//	// result: 143045
//
//	// Convert date and time
//	result, err := converter.ConvertISO8601("2023-12-25T14:30:45.123Z")
//	// result: 20231225143045
package datetimeconverter

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/relvacode/iso8601"
)

// DateTimeConverter handles conversion of ISO 8601 formatted strings to numeric formats
type DateTimeConverter struct {
	// dateOnlyRegex matches date-only patterns like "2023-12-25"
	dateOnlyRegex *regexp.Regexp
	// timeOnlyRegex matches time-only patterns like "14:30:45.123Z" or "14:30:45Z"
	timeOnlyRegex *regexp.Regexp
	// dateTimeRegex matches full datetime patterns like "2023-12-25T14:30:45.123Z"
	dateTimeRegex *regexp.Regexp
}

// DateTimeConversionError represents errors that occur during date/time conversion
type DateTimeConversionError struct {
	Input  string
	Reason string
	Err    error
}

func (e *DateTimeConversionError) Error() string {
	if e.Err != nil {
		return fmt.Sprintf("datetime conversion failed for input '%s': %s (%v)", e.Input, e.Reason, e.Err)
	}
	return fmt.Sprintf("datetime conversion failed for input '%s': %s", e.Input, e.Reason)
}

func (e *DateTimeConversionError) Unwrap() error {
	return e.Err
}

// NewDateTimeConverter creates a new DateTimeConverter with compiled regex patterns
func NewDateTimeConverter() *DateTimeConverter {
	return &DateTimeConverter{
		// Date only: YYYY-MM-DD
		dateOnlyRegex: regexp.MustCompile(`^\d{4}-\d{2}-\d{2}$`),
		// Time only: HH:mm:ss.sssZ or HH:mm:ssZ or HH:mm:ss.sss+/-HH:MM
		timeOnlyRegex: regexp.MustCompile(`^\d{2}:\d{2}:\d{2}(\.\d{3})?(Z|[+-]\d{2}:\d{2})$`),
		// DateTime: YYYY-MM-DDTHH:mm:ss.sssZ or similar with timezone
		dateTimeRegex: regexp.MustCompile(`^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?(Z|[+-]\d{2}:\d{2})$`),
	}
}

// ConvertISO8601ToInt64 converts an ISO 8601 formatted string to int64 format
// This is the preferred method for datetime features in ML models to avoid precision loss
//
// Parameters:
// - input: ISO 8601 formatted string (date, time, or datetime)
//
// Returns:
// - int64: The converted numeric value
// - error: Any error encountered during conversion
//
// Conversion rules:
// - Date only (YYYY-MM-DD): returns YYYYMMDD (8-digit number)
// - Time only (HH:mm:ss.sssZ): returns HHMMSS (6-digit number, ignoring milliseconds)
// - DateTime (YYYY-MM-DDTHH:mm:ss.sssZ): returns YYYYMMDDHHMMSS (14-digit number)
func (dtc *DateTimeConverter) ConvertISO8601ToInt64(input string) (int64, error) {
	input = strings.TrimSpace(input)

	if input == "" {
		return 0, &DateTimeConversionError{
			Input:  input,
			Reason: "input cannot be empty",
		}
	}

	// Check if it's a full datetime
	if dtc.dateTimeRegex.MatchString(input) {
		return dtc.convertDateTimeToInt64(input)
	}

	// Check if it's date only
	if dtc.dateOnlyRegex.MatchString(input) {
		return dtc.convertDateOnlyToInt64(input)
	}

	// Check if it's time only
	if dtc.timeOnlyRegex.MatchString(input) {
		return dtc.convertTimeOnlyToInt64(input)
	}

	return 0, &DateTimeConversionError{
		Input:  input,
		Reason: "input does not match expected ISO 8601 format (YYYY-MM-DD, HH:mm:ss.sssZ, or YYYY-MM-DDTHH:mm:ss.sssZ)",
	}
}

// ConvertISO8601ToFloat64 converts an ISO 8601 formatted string to float64 format
// DEPRECATED: Use ConvertISO8601ToInt64 for better precision with datetime features
//
// Parameters:
// - input: ISO 8601 formatted string (date, time, or datetime)
//
// Returns:
// - float64: The converted numeric value
// - error: Any error encountered during conversion
//
// Conversion rules:
// - Date only (YYYY-MM-DD): returns YYYYMMDD (8-digit number)
// - Time only (HH:mm:ss.sssZ): returns HHMMSS (6-digit number, ignoring milliseconds)
// - DateTime (YYYY-MM-DDTHH:mm:ss.sssZ): returns YYYYMMDDHHMMSS (14-digit number)
func (dtc *DateTimeConverter) ConvertISO8601ToFloat64(input string) (float64, error) {
	input = strings.TrimSpace(input)

	if input == "" {
		return 0, &DateTimeConversionError{
			Input:  input,
			Reason: "input cannot be empty",
		}
	}

	// Check if it's a full datetime
	if dtc.dateTimeRegex.MatchString(input) {
		return dtc.convertDateTime(input)
	}

	// Check if it's date only
	if dtc.dateOnlyRegex.MatchString(input) {
		return dtc.convertDateOnly(input)
	}

	// Check if it's time only
	if dtc.timeOnlyRegex.MatchString(input) {
		return dtc.convertTimeOnly(input)
	}

	return 0, &DateTimeConversionError{
		Input:  input,
		Reason: "input does not match expected ISO 8601 format (YYYY-MM-DD, HH:mm:ss.sssZ, or YYYY-MM-DDTHH:mm:ss.sssZ)",
	}
}

// convertDateTime converts a full datetime string to YYYYMMDDHHMMSS format
// The time is converted to UTC before formatting
func (dtc *DateTimeConverter) convertDateTime(input string) (float64, error) {
	parsedTime, err := iso8601.ParseString(input)
	if err != nil {
		return 0, &DateTimeConversionError{
			Input:  input,
			Reason: "failed to parse datetime",
			Err:    err,
		}
	}

	// Convert to UTC for consistent formatting
	utcTime := parsedTime.UTC()

	// Format as YYYYMMDDHHMMSS
	formatted := utcTime.Format("20060102150405")
	result, err := strconv.ParseFloat(formatted, 64)
	if err != nil {
		return 0, &DateTimeConversionError{
			Input:  input,
			Reason: "failed to convert formatted datetime to integer",
			Err:    err,
		}
	}

	return result, nil
}

// convertDateOnly converts a date-only string to YYYYMMDD format
func (dtc *DateTimeConverter) convertDateOnly(input string) (float64, error) {
	// Parse as date (add time component for parsing)
	parsedTime, err := time.Parse("2006-01-02", input)
	if err != nil {
		return 0, &DateTimeConversionError{
			Input:  input,
			Reason: "failed to parse date",
			Err:    err,
		}
	}

	// Format as YYYYMMDD
	formatted := parsedTime.Format("20060102")
	result, err := strconv.ParseFloat(formatted, 64)
	if err != nil {
		return 0, &DateTimeConversionError{
			Input:  input,
			Reason: "failed to convert formatted date to integer",
			Err:    err,
		}
	}

	return result, nil
}

// convertTimeOnly converts a time-only string to HHMMSS format
// The time is converted to UTC before formatting
func (dtc *DateTimeConverter) convertTimeOnly(input string) (float64, error) {
	// For time-only parsing, we need to add a date component
	// Use a fixed date (1970-01-01) and parse as full datetime
	dateTimeInput := "1970-01-01T" + input

	parsedTime, err := iso8601.ParseString(dateTimeInput)
	if err != nil {
		return 0, &DateTimeConversionError{
			Input:  input,
			Reason: "failed to parse time",
			Err:    err,
		}
	}

	// Convert to UTC for consistent formatting
	utcTime := parsedTime.UTC()

	// Format as HHMMSS (ignore milliseconds)
	formatted := utcTime.Format("150405")
	result, err := strconv.ParseFloat(formatted, 64)
	if err != nil {
		return 0, &DateTimeConversionError{
			Input:  input,
			Reason: "failed to convert formatted time to integer",
			Err:    err,
		}
	}

	return result, nil
}

// convertDateTimeToInt64 converts a full datetime string to YYYYMMDDHHMMSS format as int64
// The time is converted to UTC before formatting
func (dtc *DateTimeConverter) convertDateTimeToInt64(input string) (int64, error) {
	parsedTime, err := iso8601.ParseString(input)
	if err != nil {
		return 0, &DateTimeConversionError{
			Input:  input,
			Reason: "failed to parse datetime",
			Err:    err,
		}
	}

	// Convert to UTC for consistent formatting
	utcTime := parsedTime.UTC()

	// Format as YYYYMMDDHHMMSS
	formatted := utcTime.Format("20060102150405")
	result, err := strconv.ParseInt(formatted, 10, 64)
	if err != nil {
		return 0, &DateTimeConversionError{
			Input:  input,
			Reason: "failed to convert formatted datetime to int64",
			Err:    err,
		}
	}

	// Validate range for 14-digit datetime
	if result < 10000101000000 || result > 99991231235959 {
		return 0, &DateTimeConversionError{
			Input:  input,
			Reason: fmt.Sprintf("datetime value %d outside valid range (10000101000000-99991231235959)", result),
		}
	}

	return result, nil
}

// convertDateOnlyToInt64 converts a date-only string to YYYYMMDD format as int64
func (dtc *DateTimeConverter) convertDateOnlyToInt64(input string) (int64, error) {
	// Parse as date (add time component for parsing)
	parsedTime, err := time.Parse("2006-01-02", input)
	if err != nil {
		return 0, &DateTimeConversionError{
			Input:  input,
			Reason: "failed to parse date",
			Err:    err,
		}
	}

	// Format as YYYYMMDD
	formatted := parsedTime.Format("20060102")
	result, err := strconv.ParseInt(formatted, 10, 64)
	if err != nil {
		return 0, &DateTimeConversionError{
			Input:  input,
			Reason: "failed to convert formatted date to int64",
			Err:    err,
		}
	}

	// Validate range for 8-digit date
	if result < 10000101 || result > 99991231 {
		return 0, &DateTimeConversionError{
			Input:  input,
			Reason: fmt.Sprintf("date value %d outside valid range (10000101-99991231)", result),
		}
	}

	return result, nil
}

// convertTimeOnlyToInt64 converts a time-only string to HHMMSS format as int64
// The time is converted to UTC before formatting
func (dtc *DateTimeConverter) convertTimeOnlyToInt64(input string) (int64, error) {
	// For time-only parsing, we need to add a date component
	// Use a fixed date (1970-01-01) and parse as full datetime
	dateTimeInput := "1970-01-01T" + input

	parsedTime, err := iso8601.ParseString(dateTimeInput)
	if err != nil {
		return 0, &DateTimeConversionError{
			Input:  input,
			Reason: "failed to parse time",
			Err:    err,
		}
	}

	// Convert to UTC for consistent formatting
	utcTime := parsedTime.UTC()

	// Format as HHMMSS (ignore milliseconds)
	formatted := utcTime.Format("150405")
	result, err := strconv.ParseInt(formatted, 10, 64)
	if err != nil {
		return 0, &DateTimeConversionError{
			Input:  input,
			Reason: "failed to convert formatted time to int64",
			Err:    err,
		}
	}

	// Validate range for 6-digit time
	if result < 0 || result > 235959 {
		return 0, &DateTimeConversionError{
			Input:  input,
			Reason: fmt.Sprintf("time value %d outside valid range (000000-235959)", result),
		}
	}

	return result, nil
}

// ConvertTimeToInt64 converts a time.Time object directly to int64 format
// This is useful for converting Go time objects to ML-ready format
func (dtc *DateTimeConverter) ConvertTimeToInt64(t time.Time) (int64, error) {
	// Convert to UTC for consistent formatting
	utcTime := t.UTC()

	// Format as YYYYMMDDHHMMSS
	formatted := utcTime.Format("20060102150405")
	result, err := strconv.ParseInt(formatted, 10, 64)
	if err != nil {
		return 0, &DateTimeConversionError{
			Input:  t.String(),
			Reason: "failed to convert time.Time to int64",
			Err:    err,
		}
	}

	// Validate range for 14-digit datetime
	if result < 10000101000000 || result > 99991231235959 {
		return 0, &DateTimeConversionError{
			Input:  t.String(),
			Reason: fmt.Sprintf("datetime value %d outside valid range (10000101000000-99991231235959)", result),
		}
	}

	return result, nil
}

// ConvertInt64ToTime converts an int64 datetime back to time.Time
// This is useful for converting ML format back to Go time objects
func (dtc *DateTimeConverter) ConvertInt64ToTime(int64Dt int64) (time.Time, error) {
	if int64Dt < 10000101000000 || int64Dt > 99991231235959 {
		return time.Time{}, &DateTimeConversionError{
			Input:  fmt.Sprintf("%d", int64Dt),
			Reason: fmt.Sprintf("int64 datetime %d outside valid range (10000101000000-99991231235959)", int64Dt),
		}
	}

	dtStr := fmt.Sprintf("%014d", int64Dt)

	year, _ := strconv.Atoi(dtStr[0:4])
	month, _ := strconv.Atoi(dtStr[4:6])
	day, _ := strconv.Atoi(dtStr[6:8])
	hour, _ := strconv.Atoi(dtStr[8:10])
	minute, _ := strconv.Atoi(dtStr[10:12])
	second, _ := strconv.Atoi(dtStr[12:14])

	return time.Date(year, time.Month(month), day, hour, minute, second, 0, time.UTC), nil
}

// BatchConvertToInt64 efficiently converts multiple time.Time objects to int64 format
func (dtc *DateTimeConverter) BatchConvertToInt64(times []time.Time) ([]int64, error) {
	result := make([]int64, len(times))

	for i, t := range times {
		int64Val, err := dtc.ConvertTimeToInt64(t)
		if err != nil {
			return nil, fmt.Errorf("error converting time at index %d: %w", i, err)
		}
		result[i] = int64Val
	}

	return result, nil
}

// BatchConvertISO8601ToInt64 efficiently converts multiple ISO 8601 strings to int64 format
func (dtc *DateTimeConverter) BatchConvertISO8601ToInt64(inputs []string) ([]int64, error) {
	result := make([]int64, len(inputs))

	for i, input := range inputs {
		int64Val, err := dtc.ConvertISO8601ToInt64(input)
		if err != nil {
			return nil, fmt.Errorf("error converting ISO 8601 string at index %d (%s): %w", i, input, err)
		}
		result[i] = int64Val
	}

	return result, nil
}

// IsValidDateTimeInt64 checks if an int64 value represents a valid datetime
func (dtc *DateTimeConverter) IsValidDateTimeInt64(int64Dt int64) bool {
	if int64Dt < 10000101000000 || int64Dt > 99991231235959 {
		return false
	}

	// Try to convert back to time.Time to validate the actual date/time components
	_, err := dtc.ConvertInt64ToTime(int64Dt)
	return err == nil
}

// FormatDateTimeInt64 formats an int64 datetime value for human-readable display
func (dtc *DateTimeConverter) FormatDateTimeInt64(int64Dt int64, layout string) (string, error) {
	t, err := dtc.ConvertInt64ToTime(int64Dt)
	if err != nil {
		return "", err
	}

	return t.Format(layout), nil
}

// GetDateTimeComponents extracts year, month, day, hour, minute, second from int64 datetime
func (dtc *DateTimeConverter) GetDateTimeComponents(int64Dt int64) (year, month, day, hour, minute, second int, err error) {
	if int64Dt < 10000101000000 || int64Dt > 99991231235959 {
		return 0, 0, 0, 0, 0, 0, &DateTimeConversionError{
			Input:  fmt.Sprintf("%d", int64Dt),
			Reason: fmt.Sprintf("int64 datetime %d outside valid range", int64Dt),
		}
	}

	dtStr := fmt.Sprintf("%014d", int64Dt)

	year, _ = strconv.Atoi(dtStr[0:4])
	month, _ = strconv.Atoi(dtStr[4:6])
	day, _ = strconv.Atoi(dtStr[6:8])
	hour, _ = strconv.Atoi(dtStr[8:10])
	minute, _ = strconv.Atoi(dtStr[10:12])
	second, _ = strconv.Atoi(dtStr[12:14])

	return year, month, day, hour, minute, second, nil
}
