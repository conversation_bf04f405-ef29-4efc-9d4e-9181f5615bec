package datetimeconverter

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// Tests for new int64 functionality
func TestConvertISO8601ToInt64_DateTime(t *testing.T) {
	converter := NewDateTimeConverter()

	tests := []struct {
		name     string
		input    string
		expected int64
		hasError bool
	}{
		{
			name:     "full datetime with milliseconds and Z",
			input:    "2023-12-25T14:30:45.123Z",
			expected: 20231225143045,
			hasError: false,
		},
		{
			name:     "full datetime without milliseconds and Z",
			input:    "2023-12-25T14:30:45Z",
			expected: 20231225143045,
			hasError: false,
		},
		{
			name:     "datetime with timezone offset",
			input:    "2023-12-25T14:30:45.123+05:30",
			expected: 20231225090045, // Converted to UTC (14:30 - 5:30 = 09:00)
			hasError: false,
		},
		{
			name:     "datetime with negative timezone offset",
			input:    "2023-12-25T14:30:45.123-08:00",
			expected: 20231225223045, // Converted to UTC (14:30 + 8:00 = 22:30)
			hasError: false,
		},
		{
			name:     "new year datetime",
			input:    "2024-01-01T00:00:00Z",
			expected: 20240101000000,
			hasError: false,
		},
		{
			name:     "maximum valid datetime",
			input:    "9999-12-31T23:59:59Z",
			expected: 99991231235959,
			hasError: false,
		},
		{
			name:     "minimum valid datetime",
			input:    "1000-01-01T00:00:00Z",
			expected: 10000101000000,
			hasError: false,
		},
		{
			name:     "invalid datetime - missing T separator",
			input:    "2023-12-25 14:30:45Z",
			expected: 0,
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := converter.ConvertISO8601ToInt64(tt.input)
			if tt.hasError {
				assert.Error(t, err)
				assert.IsType(t, &DateTimeConversionError{}, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestConvertISO8601ToInt64_DateOnly(t *testing.T) {
	converter := NewDateTimeConverter()

	tests := []struct {
		name     string
		input    string
		expected int64
		hasError bool
	}{
		{
			name:     "valid date",
			input:    "2023-12-25",
			expected: 20231225,
			hasError: false,
		},
		{
			name:     "valid date with leading zeros",
			input:    "2023-01-05",
			expected: 20230105,
			hasError: false,
		},
		{
			name:     "leap year date",
			input:    "2024-02-29",
			expected: 20240229,
			hasError: false,
		},
		{
			name:     "maximum valid date",
			input:    "9999-12-31",
			expected: 99991231,
			hasError: false,
		},
		{
			name:     "minimum valid date",
			input:    "1000-01-01",
			expected: 10000101,
			hasError: false,
		},
		{
			name:     "invalid date format",
			input:    "2023/12/25",
			expected: 0,
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := converter.ConvertISO8601ToInt64(tt.input)
			if tt.hasError {
				assert.Error(t, err)
				assert.IsType(t, &DateTimeConversionError{}, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestConvertISO8601ToInt64_TimeOnly(t *testing.T) {
	converter := NewDateTimeConverter()

	tests := []struct {
		name     string
		input    string
		expected int64
		hasError bool
	}{
		{
			name:     "time with milliseconds and Z",
			input:    "14:30:45.123Z",
			expected: 143045,
			hasError: false,
		},
		{
			name:     "time without milliseconds and Z",
			input:    "14:30:45Z",
			expected: 143045,
			hasError: false,
		},
		{
			name:     "midnight time",
			input:    "00:00:00Z",
			expected: 0,
			hasError: false,
		},
		{
			name:     "end of day time",
			input:    "23:59:59Z",
			expected: 235959,
			hasError: false,
		},
		{
			name:     "invalid time format - no timezone",
			input:    "14:30:45",
			expected: 0,
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := converter.ConvertISO8601ToInt64(tt.input)
			if tt.hasError {
				assert.Error(t, err)
				assert.IsType(t, &DateTimeConversionError{}, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestConvertTimeToInt64(t *testing.T) {
	converter := NewDateTimeConverter()

	tests := []struct {
		name     string
		input    time.Time
		expected int64
		hasError bool
	}{
		{
			name:     "valid datetime",
			input:    time.Date(2024, 12, 1, 14, 30, 45, 0, time.UTC),
			expected: 20241201143045,
			hasError: false,
		},
		{
			name:     "minimum valid datetime",
			input:    time.Date(1000, 1, 1, 0, 0, 0, 0, time.UTC),
			expected: 10000101000000,
			hasError: false,
		},
		{
			name:     "maximum valid datetime",
			input:    time.Date(9999, 12, 31, 23, 59, 59, 0, time.UTC),
			expected: 99991231235959,
			hasError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := converter.ConvertTimeToInt64(tt.input)
			if tt.hasError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestConvertInt64ToTime(t *testing.T) {
	converter := NewDateTimeConverter()

	tests := []struct {
		name     string
		input    int64
		expected time.Time
		hasError bool
	}{
		{
			name:     "valid datetime",
			input:    20241201143045,
			expected: time.Date(2024, 12, 1, 14, 30, 45, 0, time.UTC),
			hasError: false,
		},
		{
			name:     "minimum valid datetime",
			input:    10000101000000,
			expected: time.Date(1000, 1, 1, 0, 0, 0, 0, time.UTC),
			hasError: false,
		},
		{
			name:     "maximum valid datetime",
			input:    99991231235959,
			expected: time.Date(9999, 12, 31, 23, 59, 59, 0, time.UTC),
			hasError: false,
		},
		{
			name:     "invalid datetime - too small",
			input:    123456789,
			expected: time.Time{},
			hasError: true,
		},
		{
			name:     "invalid datetime - too large",
			input:    999999999999999,
			expected: time.Time{},
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := converter.ConvertInt64ToTime(tt.input)
			if tt.hasError {
				assert.Error(t, err)
				assert.IsType(t, &DateTimeConversionError{}, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestRoundTripConversion(t *testing.T) {
	converter := NewDateTimeConverter()

	testTimes := []time.Time{
		time.Date(2024, 1, 15, 9, 30, 0, 0, time.UTC),
		time.Date(2024, 6, 20, 14, 45, 30, 0, time.UTC),
		time.Date(2024, 12, 31, 23, 59, 59, 0, time.UTC),
		time.Date(1000, 1, 1, 0, 0, 0, 0, time.UTC),
		time.Date(9999, 12, 31, 23, 59, 59, 0, time.UTC),
	}

	for _, original := range testTimes {
		t.Run(fmt.Sprintf("round_trip_%s", original.Format("2006-01-02T15:04:05")), func(t *testing.T) {
			// Convert to int64
			int64Val, err := converter.ConvertTimeToInt64(original)
			assert.NoError(t, err)

			// Convert back to time.Time
			converted, err := converter.ConvertInt64ToTime(int64Val)
			assert.NoError(t, err)

			// Compare (should be exactly equal since we're using UTC and no nanoseconds)
			assert.Equal(t, original, converted)
		})
	}
}
