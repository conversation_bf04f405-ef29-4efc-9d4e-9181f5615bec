package training

import (
	"context"

	"github.com/berrijam/mulberri/pkg/models"
)

// Core constants
const (
	baseFloatTolerance     = 1e-10
	maxPooledSliceCapacity = 1024
	maxPooledMapSize       = 256
)

// ImpurityCriterion defines the method used to calculate node impurity
type ImpurityCriterion int

const (
	EntropyImpurity ImpurityCriterion = iota
	GiniImpurity
	MSEImpurity
)

// Core interfaces
type Splitter[T comparable] interface {
	FindBestSplit(ctx context.Context, dataset Dataset[T], features []*models.Feature) (*SplitResult[T], error)
	CalculateImpurity(dataset Dataset[T]) (float64, error)
}

type Dataset[T comparable] interface {
	GetSize() int
	GetFeatureValue(sampleIdx int, feature *models.Feature) (interface{}, error)
	GetTarget(sampleIdx int) (T, error)
	GetIndices() []int
	Subset(indices []int) Dataset[T]
}

// Core data types
type SplitResult[T comparable] struct {
	Feature        *models.Feature
	Threshold      float64 // For numeric features (legacy)
	ThresholdInt64 int64   // For datetime features (int64 format)
	InfoGain       float64
	GainRatio      float64
	LeftIndices    []int
	RightIndices   []int
	Partitions     map[interface{}][]int
	DateTimeInfo   *models.DateTimeSplitInfo // Human-readable datetime split info
}

type numericValue[T comparable] struct {
	Value  float64
	Index  int
	Target T
}

type SplitWorkerResult[T comparable] struct {
	Split *SplitResult[T]
	Err   error
}
