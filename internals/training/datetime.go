package training

import (
	"fmt"
	"math"
	"sort"
	"time"

	datetimeconverter "github.com/berrijam/mulberri/internals/utils/datetime-converter"
	"github.com/berrijam/mulberri/pkg/models"
)

// DateTimeValue represents a datetime value with its associated target and index
type DateTimeValue[T comparable] struct {
	Value  int64 // DateTime in int64 format (YYYYMMDDHHMMSS)
	Target T     // Target value for this sample
	Index  int   // Original index in dataset
}

// PreSortedDateTimeData holds pre-sorted datetime data for efficient splitting
type PreSortedDateTimeData[T comparable] struct {
	Feature *models.Feature
	Values  []DateTimeValue[T]
	Sorted  bool
}

// DateTimeFeatureCache caches pre-sorted datetime data for performance
type DateTimeFeatureCache[T comparable] struct {
	cache map[string]*PreSortedDateTimeData[T]
}

// NewDateTimeFeatureCache creates a new datetime feature cache
func NewDateTimeFeatureCache[T comparable]() *DateTimeFeatureCache[T] {
	return &DateTimeFeatureCache[T]{
		cache: make(map[string]*PreSortedDateTimeData[T]),
	}
}

// Get retrieves cached datetime data for a feature
func (c *DateTimeFeatureCache[T]) Get(featureID string) (*PreSortedDateTimeData[T], bool) {
	data, found := c.cache[featureID]
	return data, found
}

// Set stores datetime data in the cache
func (c *DateTimeFeatureCache[T]) Set(featureID string, data *PreSortedDateTimeData[T]) {
	c.cache[featureID] = data
}

// Clear removes all cached data
func (c *DateTimeFeatureCache[T]) Clear() {
	c.cache = make(map[string]*PreSortedDateTimeData[T])
}

// evaluateDateTimeSplit finds the best threshold for a datetime feature using int64 format
func (c *C45Splitter[T]) evaluateDateTimeSplit(dataset Dataset[T], feature *models.Feature, baseImpurity float64) (*SplitResult[T], error) {
	// Check if we have pre-sorted data for this feature
	featureID := feature.Name
	if c.dateTimeCache == nil {
		c.dateTimeCache = NewDateTimeFeatureCache[T]()
	}

	if sortedData, found := c.dateTimeCache.Get(featureID); found && sortedData.Sorted {
		// Filter values for current dataset indices
		values := c.filterSortedDateTimeValues(sortedData.Values, dataset.GetIndices())
		if len(values) < c.config.MinSamplesSplit {
			return nil, nil
		}
		return c.findBestDateTimeThreshold(values, baseImpurity, feature)
	}

	// Collect and sort values (first time for this feature)
	values, err := c.collectDateTimeValues(dataset, feature)
	if err != nil {
		return nil, &SplitError{Op: "collect_datetime_values", Feature: feature.Name, Err: err}
	}
	if len(values) < c.config.MinSamplesSplit {
		return nil, nil
	}

	sort.Slice(values, func(i, j int) bool {
		return values[i].Value < values[j].Value
	})

	// Cache the sorted data for future use
	c.dateTimeCache.Set(featureID, &PreSortedDateTimeData[T]{
		Feature: feature,
		Values:  values,
		Sorted:  true,
	})

	return c.findBestDateTimeThreshold(values, baseImpurity, feature)
}

// collectDateTimeValues extracts datetime values from the dataset and converts them to int64
func (c *C45Splitter[T]) collectDateTimeValues(dataset Dataset[T], feature *models.Feature) ([]DateTimeValue[T], error) {
	converter := datetimeconverter.NewDateTimeConverter()
	indices := dataset.GetIndices()
	values := make([]DateTimeValue[T], 0, len(indices))

	for _, idx := range indices {
		// Get the raw value from dataset
		rawValue, err := dataset.GetFeatureValue(idx, feature)
		if err != nil {
			return nil, fmt.Errorf("failed to get feature value at index %d: %w", idx, err)
		}

		// Convert to int64 datetime format
		var int64Value int64
		switch v := rawValue.(type) {
		case string:
			// Assume ISO 8601 format
			int64Value, err = converter.ConvertISO8601ToInt64(v)
			if err != nil {
				return nil, fmt.Errorf("failed to convert datetime string '%s' at index %d: %w", v, idx, err)
			}
		case time.Time:
			// Convert time.Time to int64
			int64Value, err = converter.ConvertTimeToInt64(v)
			if err != nil {
				return nil, fmt.Errorf("failed to convert time.Time at index %d: %w", idx, err)
			}
		case int64:
			// Already in int64 format, validate range
			if v < 10000101000000 || v > 99991231235959 {
				return nil, fmt.Errorf("datetime value %d at index %d outside valid range", v, idx)
			}
			int64Value = v
		case float64:
			// Convert from float64 (legacy support)
			int64Value = int64(v)
			if int64Value < 10000101000000 || int64Value > 99991231235959 {
				return nil, fmt.Errorf("datetime value %d at index %d outside valid range", int64Value, idx)
			}
		default:
			return nil, fmt.Errorf("unsupported datetime value type %T at index %d", rawValue, idx)
		}

		target, err := dataset.GetTarget(idx)
		if err != nil {
			return nil, fmt.Errorf("failed to get target at index %d: %w", idx, err)
		}

		values = append(values, DateTimeValue[T]{
			Value:  int64Value,
			Target: target,
			Index:  idx,
		})
	}

	return values, nil
}

// filterSortedDateTimeValues filters pre-sorted datetime values based on current dataset indices
func (c *C45Splitter[T]) filterSortedDateTimeValues(allValues []DateTimeValue[T], indices []int) []DateTimeValue[T] {
	// Create a set of indices for O(1) lookup
	indexSet := make(map[int]bool, len(indices))
	for _, idx := range indices {
		indexSet[idx] = true
	}

	// Filter values
	filtered := make([]DateTimeValue[T], 0, len(indices))
	for _, value := range allValues {
		if indexSet[value.Index] {
			filtered = append(filtered, value)
		}
	}

	return filtered
}

// findBestDateTimeThreshold finds the optimal split threshold for datetime values
func (c *C45Splitter[T]) findBestDateTimeThreshold(values []DateTimeValue[T], baseImpurity float64, feature *models.Feature) (*SplitResult[T], error) {
	if len(values) < c.config.MinSamplesSplit {
		return nil, nil
	}

	bestGainRatio := -1.0
	var bestThreshold int64
	var bestLeftIndices, bestRightIndices []int

	// Try splits between consecutive unique values
	for i := 0; i < len(values)-1; i++ {
		// Skip if values are the same
		if values[i].Value == values[i+1].Value {
			continue
		}

		// Calculate threshold as midpoint (aligned to meaningful datetime boundaries)
		threshold := c.alignDateTimeThreshold((values[i].Value + values[i+1].Value) / 2)

		// Split data
		leftIndices := make([]int, 0)
		rightIndices := make([]int, 0)
		leftTargets := make([]T, 0)
		rightTargets := make([]T, 0)

		for _, value := range values {
			if value.Value <= threshold {
				leftIndices = append(leftIndices, value.Index)
				leftTargets = append(leftTargets, value.Target)
			} else {
				rightIndices = append(rightIndices, value.Index)
				rightTargets = append(rightTargets, value.Target)
			}
		}

		// Check minimum samples constraints
		if len(leftIndices) < c.config.MinSamplesLeaf || len(rightIndices) < c.config.MinSamplesLeaf {
			continue
		}

		// Calculate gain ratio
		gainRatio := c.calculateDateTimeGainRatio(leftTargets, rightTargets, baseImpurity)

		if gainRatio > bestGainRatio {
			bestGainRatio = gainRatio
			bestThreshold = threshold
			bestLeftIndices = leftIndices
			bestRightIndices = rightIndices
		}
	}

	if bestGainRatio <= 0 {
		return nil, nil
	}

	// Create datetime info for interpretability
	converter := datetimeconverter.NewDateTimeConverter()
	dateTimeInfo := &models.DateTimeSplitInfo{}

	if t, err := converter.ConvertInt64ToTime(bestThreshold); err == nil {
		dateTimeInfo.ThresholdFormatted = t.Format("2006-01-02 15:04:05")
		dateTimeInfo.SplitDescription = fmt.Sprintf("Before %s", t.Format("January 2, 2006 15:04"))
	} else {
		dateTimeInfo.ThresholdFormatted = fmt.Sprintf("%014d", bestThreshold)
		dateTimeInfo.SplitDescription = fmt.Sprintf("Before datetime %014d", bestThreshold)
	}

	return &SplitResult[T]{
		Feature:        feature,
		ThresholdInt64: bestThreshold,
		LeftIndices:    bestLeftIndices,
		RightIndices:   bestRightIndices,
		GainRatio:      bestGainRatio,
		DateTimeInfo:   dateTimeInfo,
	}, nil
}

// alignDateTimeThreshold aligns threshold to meaningful datetime boundaries
func (c *C45Splitter[T]) alignDateTimeThreshold(threshold int64) int64 {
	converter := datetimeconverter.NewDateTimeConverter()

	// Convert to time, align to hour boundary, convert back
	if t, err := converter.ConvertInt64ToTime(threshold); err == nil {
		// Align to hour boundary (set minutes and seconds to 0)
		aligned := time.Date(t.Year(), t.Month(), t.Day(), t.Hour(), 0, 0, 0, t.Location())

		if alignedInt64, err := converter.ConvertTimeToInt64(aligned); err == nil {
			return alignedInt64
		}
	}

	// Return original if conversion fails
	return threshold
}

// calculateDateTimeGainRatio calculates gain ratio for datetime splits
func (c *C45Splitter[T]) calculateDateTimeGainRatio(leftTargets, rightTargets []T, baseImpurity float64) float64 {
	totalSamples := len(leftTargets) + len(rightTargets)
	if totalSamples == 0 {
		return 0
	}

	leftWeight := float64(len(leftTargets)) / float64(totalSamples)
	rightWeight := float64(len(rightTargets)) / float64(totalSamples)

	// Calculate impurities
	leftImpurity := calculateTargetImpurity(leftTargets)
	rightImpurity := calculateTargetImpurity(rightTargets)

	// Calculate information gain
	weightedImpurity := leftWeight*leftImpurity + rightWeight*rightImpurity
	informationGain := baseImpurity - weightedImpurity

	// Calculate split information for gain ratio
	splitInfo := 0.0
	if leftWeight > 0 {
		splitInfo -= leftWeight * math.Log2(leftWeight)
	}
	if rightWeight > 0 {
		splitInfo -= rightWeight * math.Log2(rightWeight)
	}

	// Avoid division by zero
	if splitInfo == 0 {
		return 0
	}

	return informationGain / splitInfo
}

// calculateTargetImpurity calculates Gini impurity for a set of target values
func calculateTargetImpurity[T comparable](targets []T) float64 {
	if len(targets) == 0 {
		return 0
	}

	// Count frequencies
	counts := make(map[T]int)
	for _, target := range targets {
		counts[target]++
	}

	// Calculate Gini impurity
	impurity := 1.0
	total := float64(len(targets))
	for _, count := range counts {
		prob := float64(count) / total
		impurity -= prob * prob
	}

	return impurity
}
