// Package main demonstrates complete datetime tree training workflow
package main

import (
	"fmt"
	"log"
	"time"

	datetimeconverter "github.com/berrijam/mulberri/internals/utils/datetime-converter"
	"github.com/berrijam/mulberri/pkg/models"
)

// SampleData represents a training sample with datetime features
type SampleData struct {
	TransactionTime time.Time
	Amount          float64
	MerchantType    string
	IsFraud         bool
}

func main() {
	fmt.Println("=== Mulberri DateTime Tree Training Example ===\n")

	// 1. Create sample dataset with datetime features
	dataset := createSampleDataset()
	fmt.Printf("Created dataset with %d samples\n", len(dataset))

	// 2. Demonstrate datetime preprocessing
	preprocessedData := preprocessDateTimeFeatures(dataset)
	fmt.Printf("Preprocessed %d datetime features\n", len(preprocessedData))

	// 3. Create features for tree training
	features := createFeatures()
	fmt.Printf("Created %d features for training\n", len(features))

	// 4. Demonstrate datetime-aware tree node creation
	demonstrateTreeNodeCreation(preprocessedData, features)

	// 5. Show datetime split interpretability
	demonstrateSplitInterpretability()
}

func createSampleDataset() []SampleData {
	// Create realistic transaction data for fraud detection
	samples := []SampleData{
		// Normal business hours transactions (likely legitimate)
		{time.Date(2024, 1, 15, 9, 30, 0, 0, time.UTC), 45.50, "grocery", false},
		{time.Date(2024, 1, 15, 14, 20, 0, 0, time.UTC), 125.75, "restaurant", false},
		{time.Date(2024, 1, 16, 10, 45, 0, 0, time.UTC), 89.25, "retail", false},
		{time.Date(2024, 1, 16, 16, 30, 0, 0, time.UTC), 67.80, "gas_station", false},
		{time.Date(2024, 1, 17, 12, 15, 0, 0, time.UTC), 234.50, "electronics", false},
		
		// Late night transactions with high amounts (potentially fraudulent)
		{time.Date(2024, 1, 15, 23, 45, 0, 0, time.UTC), 1500.00, "online", true},
		{time.Date(2024, 1, 16, 2, 30, 0, 0, time.UTC), 2500.00, "online", true},
		{time.Date(2024, 1, 17, 1, 15, 0, 0, time.UTC), 3200.00, "atm", true},
		
		// Weekend transactions (mixed)
		{time.Date(2024, 1, 20, 11, 0, 0, 0, time.UTC), 78.90, "restaurant", false},
		{time.Date(2024, 1, 20, 22, 30, 0, 0, time.UTC), 1800.00, "online", true},
		{time.Date(2024, 1, 21, 15, 45, 0, 0, time.UTC), 156.25, "retail", false},
		
		// Early morning transactions
		{time.Date(2024, 1, 18, 6, 0, 0, 0, time.UTC), 25.50, "coffee_shop", false},
		{time.Date(2024, 1, 18, 4, 30, 0, 0, time.UTC), 5000.00, "online", true},
	}

	return samples
}

func preprocessDateTimeFeatures(dataset []SampleData) []map[string]interface{} {
	fmt.Println("\n1. DateTime Preprocessing")
	fmt.Println("=========================")

	converter := datetimeconverter.NewDateTimeConverter()
	preprocessed := make([]map[string]interface{}, len(dataset))

	for i, sample := range dataset {
		// Convert datetime to int64 format
		transactionTimeInt64, err := converter.ConvertTimeToInt64(sample.TransactionTime)
		if err != nil {
			log.Fatalf("Error converting transaction time: %v", err)
		}

		// Create preprocessed sample
		preprocessed[i] = map[string]interface{}{
			"transaction_time_int64": transactionTimeInt64,
			"transaction_time_orig":  sample.TransactionTime, // Keep original for reference
			"amount":                 sample.Amount,
			"merchant_type":          sample.MerchantType,
			"is_fraud":               sample.IsFraud,
		}

		// Show first few conversions
		if i < 3 {
			fmt.Printf("  Sample %d: %s -> %d\n", 
				i+1, 
				sample.TransactionTime.Format("2006-01-02 15:04:05"), 
				transactionTimeInt64)
		}
	}

	return preprocessed
}

func createFeatures() []*models.Feature {
	fmt.Println("\n2. Feature Creation")
	fmt.Println("===================")

	features := make([]*models.Feature, 0)

	// Create datetime feature
	transactionTimeFeature, err := models.NewFeature("transaction_time_int64", models.DateFeature, 0)
	if err != nil {
		log.Fatalf("Error creating datetime feature: %v", err)
	}
	features = append(features, transactionTimeFeature)
	fmt.Printf("  Created datetime feature: %s\n", transactionTimeFeature.Name)

	// Create numeric feature
	amountFeature, err := models.NewFeature("amount", models.NumericFeature, 1)
	if err != nil {
		log.Fatalf("Error creating amount feature: %v", err)
	}
	features = append(features, amountFeature)
	fmt.Printf("  Created numeric feature: %s\n", amountFeature.Name)

	// Create categorical feature
	merchantFeature, err := models.NewFeature("merchant_type", models.CategoricalFeature, 2)
	if err != nil {
		log.Fatalf("Error creating merchant feature: %v", err)
	}
	features = append(features, merchantFeature)
	fmt.Printf("  Created categorical feature: %s\n", merchantFeature.Name)

	return features
}

func demonstrateTreeNodeCreation(data []map[string]interface{}, features []*models.Feature) {
	fmt.Println("\n3. DateTime Tree Node Creation")
	fmt.Println("==============================")

	converter := datetimeconverter.NewDateTimeConverter()

	// Find a good datetime split threshold (e.g., 8 PM = 20:00)
	splitTime := time.Date(2024, 1, 15, 20, 0, 0, 0, time.UTC)
	thresholdInt64, err := converter.ConvertTimeToInt64(splitTime)
	if err != nil {
		log.Fatalf("Error converting split time: %v", err)
	}

	// Create datetime split info
	dateTimeInfo := &models.DateTimeSplitInfo{
		ThresholdFormatted: splitTime.Format("2006-01-02 15:04:05"),
		SplitDescription:   fmt.Sprintf("Before %s (8 PM)", splitTime.Format("January 2, 2006 15:04")),
	}

	// Create datetime decision node
	datetimeFeature := features[0] // transaction_time_int64
	rootNode, err := models.NewDateTimeDecisionNode(datetimeFeature, thresholdInt64, dateTimeInfo)
	if err != nil {
		log.Fatalf("Error creating datetime decision node: %v", err)
	}

	fmt.Printf("  Created root node with datetime split:\n")
	fmt.Printf("    Feature: %s\n", rootNode.Feature.Name)
	fmt.Printf("    Threshold: %d\n", rootNode.ThresholdInt64)
	fmt.Printf("    Description: %s\n", rootNode.DateTimeInfo.SplitDescription)

	// Split data based on datetime threshold
	leftSamples := 0
	rightSamples := 0
	leftFraud := 0
	rightFraud := 0

	for _, sample := range data {
		transactionTime := sample["transaction_time_int64"].(int64)
		isFraud := sample["is_fraud"].(bool)

		if transactionTime <= thresholdInt64 {
			leftSamples++
			if isFraud {
				leftFraud++
			}
		} else {
			rightSamples++
			if isFraud {
				rightFraud++
			}
		}
	}

	fmt.Printf("  Split results:\n")
	fmt.Printf("    Left (before 8 PM): %d samples, %d fraud (%.1f%%)\n", 
		leftSamples, leftFraud, float64(leftFraud)/float64(leftSamples)*100)
	fmt.Printf("    Right (after 8 PM): %d samples, %d fraud (%.1f%%)\n", 
		rightSamples, rightFraud, float64(rightFraud)/float64(rightSamples)*100)

	// Create child nodes
	leftClassDist := map[interface{}]int{"legitimate": leftSamples - leftFraud, "fraud": leftFraud}
	rightClassDist := map[interface{}]int{"legitimate": rightSamples - rightFraud, "fraud": rightFraud}

	leftChild, err := models.NewLeafNode("legitimate", leftClassDist, leftSamples)
	if err != nil {
		log.Printf("Error creating left child: %v", err)
	} else {
		rootNode.SetLeftChild(leftChild)
		fmt.Printf("    Created left child: mostly legitimate transactions\n")
	}

	rightChild, err := models.NewLeafNode("fraud", rightClassDist, rightSamples)
	if err != nil {
		log.Printf("Error creating right child: %v", err)
	} else {
		rootNode.SetRightChild(rightChild)
		fmt.Printf("    Created right child: higher fraud rate\n")
	}
}

func demonstrateSplitInterpretability() {
	fmt.Println("\n4. DateTime Split Interpretability")
	fmt.Println("===================================")

	converter := datetimeconverter.NewDateTimeConverter()

	// Demonstrate various meaningful datetime splits
	splitTimes := []struct {
		time        time.Time
		description string
	}{
		{time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC), "New Year boundary"},
		{time.Date(2024, 6, 15, 12, 0, 0, 0, time.UTC), "Mid-year noon"},
		{time.Date(2024, 12, 25, 18, 0, 0, 0, time.UTC), "Christmas evening"},
		{time.Date(2024, 3, 15, 9, 0, 0, 0, time.UTC), "Business hours start"},
		{time.Date(2024, 7, 4, 22, 0, 0, 0, time.UTC), "Late evening holiday"},
	}

	for i, split := range splitTimes {
		thresholdInt64, err := converter.ConvertTimeToInt64(split.time)
		if err != nil {
			log.Printf("Error converting split time: %v", err)
			continue
		}

		dateTimeInfo := &models.DateTimeSplitInfo{
			ThresholdFormatted: split.time.Format("2006-01-02 15:04:05"),
			SplitDescription:   fmt.Sprintf("Before %s", split.time.Format("January 2, 2006 15:04")),
		}

		fmt.Printf("  Split %d (%s):\n", i+1, split.description)
		fmt.Printf("    Int64 threshold: %d\n", thresholdInt64)
		fmt.Printf("    Formatted: %s\n", dateTimeInfo.ThresholdFormatted)
		fmt.Printf("    Description: %s\n", dateTimeInfo.SplitDescription)

		// Show datetime components
		year, month, day, hour, minute, second, err := converter.GetDateTimeComponents(thresholdInt64)
		if err == nil {
			fmt.Printf("    Components: %04d-%02d-%02d %02d:%02d:%02d\n", 
				year, month, day, hour, minute, second)
		}
		fmt.Println()
	}
}

func demonstratePerformanceComparison() {
	fmt.Println("\n5. Performance Comparison: int64 vs float64")
	fmt.Println("============================================")

	converter := datetimeconverter.NewDateTimeConverter()

	// Create test datetime
	testTime := time.Date(2024, 6, 15, 14, 30, 45, 0, time.UTC)
	
	// Convert to both formats
	int64Val, _ := converter.ConvertTimeToInt64(testTime)
	float64Val, _ := converter.ConvertISO8601ToFloat64(testTime.Format(time.RFC3339))

	fmt.Printf("  Original time: %s\n", testTime.Format("2006-01-02 15:04:05"))
	fmt.Printf("  As int64: %d\n", int64Val)
	fmt.Printf("  As float64: %.0f\n", float64Val)

	// Demonstrate precision
	fmt.Printf("  Precision comparison:\n")
	fmt.Printf("    int64 == float64: %t\n", int64Val == int64(float64Val))
	fmt.Printf("    Difference: %d\n", int64Val-int64(float64Val))

	// Show memory usage
	fmt.Printf("  Memory usage:\n")
	fmt.Printf("    int64: 8 bytes\n")
	fmt.Printf("    float64: 8 bytes\n")
	fmt.Printf("    Advantage: int64 provides exact representation\n")
}
